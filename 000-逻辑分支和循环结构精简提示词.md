为这个提示词设计一套全新的提示词，输出为 .md ，细节不要省略，因为是法律所以精益求精。需求非常明确：用流程图（或思维导图）重构一个原本很多行的复杂Prompt，通过逻辑分支和循环结构来精简提示词，同时保持控制力和清晰度。这是一个非常高效的思路！以下是具体实现方法：

---

### **1. 为什么流程图能替代冗长Prompt？**
- **逻辑代替描述**：用图形化条件判断（如菱形框）替代大量“如果XX则YY”的文本规则。
- **循环复用**：通过“返回某一步”避免重复写相似提示。
- **分层结构**：思维导图可以折叠/展开分支，适合处理复杂嵌套逻辑。

---

### **2. 具体操作步骤**
#### **第1步：提取原Prompt的核心逻辑**
- 将原很多行Prompt拆解为**关键步骤**和**决策点**，例如：
```text
原Prompt可能包含：
- 如果用户需求是A，则执行步骤1、2、3；
- 如果步骤2的结果不满足B条件，则重新提问；
- 如果用户输入包含敏感词，则终止并报错...
```
- **提炼为流程图要素**：
- **操作步骤**（矩形）：如“生成大纲”、“检查敏感词”
- **条件判断**（菱形）：如“是否符合要求？”、“是否包含敏感词？”
- **循环/跳转**（箭头）：如“否→返回步骤1”

#### **第2步：用工具绘制流程图**
- **推荐工具**：
- **Draw.io**（免费在线）：支持AI生成流程图，可导出为图片/代码。
- **Mermaid语法**（文本生成图表）：直接嵌入Markdown，适合与AI交互。
- **XMind/MindMaster**（思维导图）：适合分层逻辑。

- **示例模板**（Mermaid语法）：
```mermaid
graph TD
A[开始] --> B[解析用户输入]
B --> C{输入合法?}
C -- 否 --> D[返回错误并终止]
C -- 是 --> E[生成初稿]
E --> F{初稿通过?}
F -- 否 --> B
F -- 是 --> G[输出结果]
```

#### **第3步：将流程图转化为精简Prompt**
- **方法**：
1. **用流程图符号定义规则**：
```
[步骤1] → [条件A?] → 是→[步骤2]; 否→[步骤1]
```
2. **用自然语言描述流程图逻辑**（替代原很多行）：
```text
请按以下流程执行：
1. 先解析输入，若非法则终止；
2. 生成初稿后，检查是否满足要求（标准：XX）；
- 不满足则重新解析输入；
3. 最终输出前验证格式（规则：YY）...
```
3. **附加说明**（如示例、边界情况）：
```text
注：所有“检查”需参考附件《标准清单》，但主流程仅按上图执行。
```

#### **第4步：验证和迭代**
- **测试**：用典型输入测试流程图Prompt，观察AI是否按预期循环或跳转。
- **优化**：若某步骤频繁退回，需细化该分支的判断条件。

---

### **3. 案例示范**
假设原Prompt是“AI生成技术文档”，冗长规则包括：反复检查术语、格式、长度等。
**重构后流程图Prompt可能如下**：
```text
▲ 请严格按此流程执行 ▲
1. [收集需求] → 2. [生成初稿] → 3. {检查术语?}
→ 否→返回1；是→4. {检查格式?}
→ 否→返回2；是→5. [输出]
注：具体检查标准见附录A（共10条），主流程仅需5行。
```

---

### **4. 优势总结**
- **长度控制**：流程逻辑可能从很多行缩短到50行核心+附录。
- **可维护性**：修改流程图中的一个节点即可全局生效，无需逐行排查。
- **AI友好**：明确的“是/否”分支能降低AI的幻觉率。

如果需要更具体的工具操作或流程图案例，可以告诉我你的原Prompt片段，我来帮你转化！